{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {"vscode": {"languageId": "plaintext"}}, "outputs": [], "source": ["# 用户频谱特征可视化\n", "\n", "本notebook用于可视化不同类型用户的频域特征。我们将展示每种用户类型的频谱图，以及它们的行为序列特征。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pickle\n", "import os\n", "import seaborn as sns\n", "\n", "# 设置绘图风格\n", "plt.style.use('ggplot')\n", "sns.set_theme(style=\"whitegrid\")\n", "\n", "# 检查文件是否存在\n", "if not os.path.exists('user_type_examples.pkl'):\n", "    print(\"请先运行模型训练，生成用户类型示例数据！\")\n", "else:\n", "    # 加载用户类型示例数据\n", "    with open('user_type_examples.pkl', 'rb') as f:\n", "        data = pickle.load(f)\n", "    \n", "    user_examples = data['user_examples']\n", "    power_spectrums = data['power_spectrums']\n", "    \n", "    print(\"数据加载成功！\")\n", "    print(f\"用户类型: {list(user_examples.keys())}\")\n", "    for type_name, examples in user_examples.items():\n", "        print(f\"{type_name}: {len(examples)} 个用户示例\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 1. 各类型用户的频谱特征\n", "\n", "我们将绘制每种用户类型的功率谱图，以观察它们在频域上的不同特征。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 绘制每种用户类型的功率谱\n", "fig, axes = plt.subplots(5, 1, figsize=(12, 15), sharex=True)\n", "\n", "user_types = [\"LPF\", \"HPF\", \"BPF\", \"BSF\", \"APF\"]\n", "type_descriptions = {\n", "    \"LPF\": \"低通滤波器用户 - 低频能量占比高，长期固定兴趣，偏好变化缓慢\",\n", "    \"HPF\": \"高通滤波器用户 - 高频能量占比大，偏好变化快，可能受流行物品影响\",\n", "    \"BPF\": \"带通滤波器用户 - 明显的峰值，周期性行为模式\",\n", "    \"BSF\": \"带阻滤波器用户 - 兴趣发展不连续，存在明显的兴趣跳跃\",\n", "    \"APF\": \"全通滤波器用户 - 频率分量均匀分布，探索与稳定并存\"\n", "}\n", "\n", "for i, user_type in enumerate(user_types):\n", "    ax = axes[i]\n", "    \n", "    # 检查是否有此类型的用户示例\n", "    if user_type in power_spectrums and len(power_spectrums[user_type]) > 0:\n", "        # 绘制每个用户的功率谱\n", "        for j, spectrum in enumerate(power_spectrums[user_type]):\n", "            freq = np.arange(len(spectrum))\n", "            ax.plot(freq, spectrum, alpha=0.7, label=f'User {j+1}')\n", "            \n", "        # 计算并绘制平均功率谱\n", "        if len(power_spectrums[user_type]) > 1:\n", "            avg_spectrum = np.mean([s for s in power_spectrums[user_type]], axis=0)\n", "            ax.plot(freq, avg_spectrum, 'k--', linewidth=2, label='Average')\n", "        \n", "        ax.set_title(f\"{user_type} - {type_descriptions[user_type]}\")\n", "        ax.set_ylabel('Power')\n", "        ax.legend(loc='upper right')\n", "    else:\n", "        ax.text(0.5, 0.5, f\"No {user_type} users found\", ha='center', va='center')\n", "        ax.set_title(f\"{user_type} - {type_descriptions[user_type]}\")\n", "\n", "axes[-1].set_xlabel('Frequency')\n", "plt.tight_layout()\n", "plt.savefig('user_power_spectrums.png', dpi=300)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 2. 用户行为序列可视化\n", "\n", "我们将可视化不同类型用户的行为序列，观察它们在时域上的特征。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 绘制用户行为序列热图\n", "fig, axes = plt.subplots(len(user_types), 1, figsize=(14, 20))\n", "\n", "for i, user_type in enumerate(user_types):\n", "    ax = axes[i]\n", "    \n", "    if user_type in user_examples and len(user_examples[user_type]) > 0:\n", "        # 创建序列矩阵，非零值为1，零值（填充）为0\n", "        sequences = np.array([(seq > 0).astype(int) for seq in user_examples[user_type]])\n", "        \n", "        # 绘制热图\n", "        im = ax.imshow(sequences, aspect='auto', cmap='Blues')\n", "        ax.set_title(f\"{user_type} - {type_descriptions[user_type]}\")\n", "        ax.set_ylabel('User')\n", "        \n", "        # 添加颜色条\n", "        if i == len(user_types) - 1:\n", "            ax.set_xlabel('Sequence Position')\n", "    else:\n", "        ax.text(0.5, 0.5, f\"No {user_type} users found\", ha='center', va='center')\n", "        ax.set_title(f\"{user_type} - {type_descriptions[user_type]}\")\n", "\n", "plt.colorbar(im, ax=axes.ravel().tolist(), label='Item Interaction (1=Yes, 0=No)')\n", "plt.tight_layout()\n", "plt.savefig('user_sequence_patterns.png', dpi=300)\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 3. 频域特征与滤波器响应\n", "\n", "我们将可视化不同类型用户的频域特征与对应的滤波器响应。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建各类型滤波器的响应函数\n", "def create_filter_responses(n_freq):\n", "    freq = np.arange(n_freq)\n", "    \n", "    # 低通滤波器（LPF）\n", "    lpf = np.exp(-freq / (n_freq * 0.3))\n", "    \n", "    # 高通滤波器（HPF）\n", "    hpf = 1 - np.exp(-freq / (n_freq * 0.3))\n", "    \n", "    # 带通滤波器（BPF）\n", "    mid = n_freq // 2\n", "    bpf = np.exp(-(freq - mid)**2 / (2 * (mid * 0.5)**2))\n", "    \n", "    # 带阻滤波器（BSF）\n", "    bsf = 1 - np.exp(-(freq - mid)**2 / (2 * (mid * 0.5)**2))\n", "    \n", "    # 全通滤波器（APF）\n", "    apf = np.ones(n_freq)\n", "    \n", "    return {\"LPF\": lpf, \"HPF\": hpf, \"BPF\": bpf, \"BSF\": bsf, \"APF\": apf}\n", "\n", "# 假设我们有功率谱数据\n", "if 'power_spectrums' in locals() and any(len(v) > 0 for v in power_spectrums.values()):\n", "    # 获取频率点数\n", "    for user_type, spectrums in power_spectrums.items():\n", "        if len(spectrums) > 0:\n", "            n_freq = len(spectrums[0])\n", "            break\n", "    \n", "    # 创建滤波器响应\n", "    filter_responses = create_filter_responses(n_freq)\n", "    \n", "    # 绘制每种用户类型的平均功率谱与对应滤波器响应\n", "    fig, axes = plt.subplots(len(user_types), 1, figsize=(12, 15), sharex=True)\n", "    \n", "    for i, user_type in enumerate(user_types):\n", "        ax = axes[i]\n", "        \n", "        if user_type in power_spectrums and len(power_spectrums[user_type]) > 0:\n", "            # 计算平均功率谱\n", "            avg_spectrum = np.mean([s for s in power_spectrums[user_type]], axis=0)\n", "            freq = np.arange(len(avg_spectrum))\n", "            \n", "            # 绘制平均功率谱\n", "            ax.plot(freq, avg_spectrum, 'b-', linewidth=2, label='Avg Power Spectrum')\n", "            \n", "            # 绘制对应滤波器响应\n", "            ax.plot(freq, filter_responses[user_type], 'r--', linewidth=2, label='Filter Response')\n", "            \n", "            ax.set_title(f\"{user_type} - {type_descriptions[user_type]}\")\n", "            ax.set_ylabel('Magnitude')\n", "            ax.legend(loc='upper right')\n", "        else:\n", "            ax.text(0.5, 0.5, f\"No {user_type} users found\", ha='center', va='center')\n", "            ax.set_title(f\"{user_type} - {type_descriptions[user_type]}\")\n", "    \n", "    axes[-1].set_xlabel('Frequency')\n", "    plt.tight_layout()\n", "    plt.savefig('user_spectrum_vs_filter.png', dpi=300)\n", "    plt.show()\n", "else:\n", "    print(\"没有可用的功率谱数据！\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## 4. 用户类型分布统计\n", "\n", "我们将统计不同类型用户的数量分布。\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 统计每种用户类型的数量\n", "user_counts = {user_type: len(examples) for user_type, examples in user_examples.items()}\n", "\n", "# 绘制用户类型分布柱状图\n", "plt.figure(figsize=(10, 6))\n", "bars = plt.bar(user_counts.keys(), user_counts.values())\n", "\n", "# 添加数据标签\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,\n", "            f'{height}',\n", "            ha='center', va='bottom')\n", "\n", "plt.title('User Type Distribution')\n", "plt.xlabel('User Type')\n", "plt.ylabel('Count')\n", "plt.tight_layout()\n", "plt.savefig('user_type_distribution.png', dpi=300)\n", "plt.show()\n", "\n", "# 计算百分比分布\n", "total_users = sum(user_counts.values())\n", "user_percentages = {user_type: count/total_users*100 for user_type, count in user_counts.items()}\n", "\n", "# 绘制饼图\n", "plt.figure(figsize=(10, 8))\n", "plt.pie(user_percentages.values(), labels=user_percentages.keys(), autopct='%1.1f%%', \n", "        startangle=90, shadow=True)\n", "plt.axis('equal')  # 使饼图为正圆形\n", "plt.title('User Type Distribution (%)')\n", "plt.tight_layout()\n", "plt.savefig('user_type_percentage.png', dpi=300)\n", "plt.show()\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}