# @Time   : 2022/2/13
# <AUTHOR> <PERSON>
# @Email  : <EMAIL>

import copy
import math
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np

def gelu(x):
    """Implementation of the gelu activation function.
        For information: OpenAI GPT's gelu is slightly different
        (and gives slightly different results):
        0.5 * x * (1 + torch.tanh(math.sqrt(2 / math.pi) *
        (x + 0.044715 * torch.pow(x, 3))))
        Also see https://arxiv.org/abs/1606.08415
    """
    return x * 0.5 * (1.0 + torch.erf(x / math.sqrt(2.0)))

def swish(x):
    return x * torch.sigmoid(x)

ACT2FN = {"gelu": gelu, "relu": F.relu, "swish": swish}

class LayerNorm(nn.Module):
    def __init__(self, hidden_size, eps=1e-12):
        """Construct a layernorm module in the TF style (epsilon inside the square root).
        """
        super(Layer<PERSON><PERSON>, self).__init__()
        self.weight = nn.Parameter(torch.ones(hidden_size))
        self.bias = nn.Parameter(torch.zeros(hidden_size))
        self.variance_epsilon = eps

    def forward(self, x):
        u = x.mean(-1, keepdim=True)
        s = (x - u).pow(2).mean(-1, keepdim=True)
        x = (x - u) / torch.sqrt(s + self.variance_epsilon)
        return self.weight * x + self.bias

class SelfAttention(nn.Module):
    def __init__(self, args):
        super(SelfAttention, self).__init__()
        if args.hidden_size % args.num_attention_heads != 0:
            raise ValueError(
                "The hidden size (%d) is not a multiple of the number of attention "
                "heads (%d)" % (args.hidden_size, args.num_attention_heads))
        self.num_attention_heads = args.num_attention_heads
        self.attention_head_size = int(args.hidden_size / args.num_attention_heads)
        self.all_head_size = self.num_attention_heads * self.attention_head_size

        self.query = nn.Linear(args.hidden_size, self.all_head_size)
        self.key = nn.Linear(args.hidden_size, self.all_head_size)
        self.value = nn.Linear(args.hidden_size, self.all_head_size)

        self.attn_dropout = nn.Dropout(args.attention_probs_dropout_prob)

        # 做完self-attention 做一个前馈全连接 LayerNorm 输出
        self.dense = nn.Linear(args.hidden_size, args.hidden_size)
        self.LayerNorm = LayerNorm(args.hidden_size, eps=1e-12)
        self.out_dropout = nn.Dropout(args.hidden_dropout_prob)

    def transpose_for_scores(self, x):
        new_x_shape = x.size()[:-1] + (self.num_attention_heads, self.attention_head_size)
        x = x.view(*new_x_shape)
        return x.permute(0, 2, 1, 3)

    def forward(self, input_tensor, attention_mask):
        mixed_query_layer = self.query(input_tensor)
        mixed_key_layer = self.key(input_tensor)
        mixed_value_layer = self.value(input_tensor)

        query_layer = self.transpose_for_scores(mixed_query_layer)
        key_layer = self.transpose_for_scores(mixed_key_layer)
        value_layer = self.transpose_for_scores(mixed_value_layer)

        # Take the dot product between "query" and "key" to get the raw attention scores.
        attention_scores = torch.matmul(query_layer, key_layer.transpose(-1, -2))

        attention_scores = attention_scores / math.sqrt(self.attention_head_size)
        # Apply the attention mask is (precomputed for all layers in BertModel forward() function)
        # [batch_size heads seq_len seq_len] scores
        # [batch_size 1 1 seq_len]
        attention_scores = attention_scores + attention_mask

        # Normalize the attention scores to probabilities.
        attention_probs = nn.Softmax(dim=-1)(attention_scores)
        # This is actually dropping out entire tokens to attend to, which might
        # seem a bit unusual, but is taken from the original Transformer paper.
        # Fixme
        attention_probs = self.attn_dropout(attention_probs)
        context_layer = torch.matmul(attention_probs, value_layer)
        context_layer = context_layer.permute(0, 2, 1, 3).contiguous()
        new_context_layer_shape = context_layer.size()[:-2] + (self.all_head_size,)
        context_layer = context_layer.view(*new_context_layer_shape)
        hidden_states = self.dense(context_layer)
        hidden_states = self.out_dropout(hidden_states)
        hidden_states = self.LayerNorm(hidden_states + input_tensor)

        return hidden_states

class UserSpectrumClassifier(nn.Module):
    """用户频谱分类器，根据用户行为序列的频域特征对用户进行分类"""
    def __init__(self, args):
        super(UserSpectrumClassifier, self).__init__()
        self.n_freq_bins = args.max_seq_length // 2 + 1  # 频域分量数量
        
        # 分类器网络
        self.classifier = nn.Sequential(
            nn.Linear(self.n_freq_bins, 64),
            nn.ReLU(),
            nn.Linear(64, 5)  # 5类：LPF, HPF, BPF, BSF, APF
        )
        
        # 频段划分参数
        self.low_freq_idx = int(self.n_freq_bins * 0.2)  # 低频区域（前20%）
        self.high_freq_idx = int(self.n_freq_bins * 0.8)  # 高频区域（后20%）
        
    def forward(self, x_fft):
        """
        Args:
            x_fft: 用户序列的频域表示 [batch, seq_len//2+1, hidden]
        Returns:
            user_types: 用户类型的概率分布 [batch, 5]
            power_spectrum: 功率谱 [batch, seq_len//2+1]
        """
        # 计算功率谱（每个频率分量的能量）
        power_spectrum = torch.abs(x_fft).mean(dim=2)  # [batch, seq_len//2+1]
        
        # 归一化功率谱
        normalized_power = power_spectrum / (torch.sum(power_spectrum, dim=1, keepdim=True) + 1e-8)
        
        # 分类用户类型
        user_types_logits = self.classifier(normalized_power)
        user_types = F.softmax(user_types_logits, dim=1)  # [batch, 5]
        
        return user_types, normalized_power

class AdaptiveFilterLayer(nn.Module):
    """自适应滤波器层，根据用户类型动态调整滤波器参数"""
    def __init__(self, args):
        super(AdaptiveFilterLayer, self).__init__()
        # 基本参数
        self.max_seq_length = args.max_seq_length
        self.hidden_size = args.hidden_size
        self.n_freq_bins = args.max_seq_length // 2 + 1
        
        # 用户频谱分类器
        self.spectrum_classifier = UserSpectrumClassifier(args)
        
        # 可学习的滤波器权重（基础滤波器）
        self.complex_weight = nn.Parameter(
            torch.randn(1, self.n_freq_bins, args.hidden_size, 2, dtype=torch.float32) * 0.02
        )
        
        # 为不同类型用户定制的滤波器调整参数
        self.filter_adjustments = nn.Parameter(
            torch.ones(5, self.n_freq_bins, dtype=torch.float32)  # 5种用户类型
        )
        
        # 低通滤波器（LPF）- 保留低频，削弱高频
        self.filter_adjustments.data[0] = torch.exp(-torch.arange(self.n_freq_bins) / (self.n_freq_bins * 0.3))
        
        # 高通滤波器（HPF）- 保留高频，削弱低频
        self.filter_adjustments.data[1] = 1 - torch.exp(-torch.arange(self.n_freq_bins) / (self.n_freq_bins * 0.3))
        
        # 带通滤波器（BPF）- 保留中频，削弱两端
        mid = self.n_freq_bins // 2
        x = torch.arange(self.n_freq_bins)
        self.filter_adjustments.data[2] = torch.exp(-(x - mid)**2 / (2 * (mid * 0.5)**2))
        
        # 带阻滤波器（BSF）- 抑制中频，保留两端
        self.filter_adjustments.data[3] = 1 - torch.exp(-(x - mid)**2 / (2 * (mid * 0.5)**2))
        
        # 全通滤波器（APF）- 均匀分布
        self.filter_adjustments.data[4] = torch.ones(self.n_freq_bins)
        
        # 其他层
        self.out_dropout = nn.Dropout(args.hidden_dropout_prob)
        self.LayerNorm = LayerNorm(args.hidden_size, eps=1e-12)

    def forward(self, input_tensor):
        """
        Args:
            input_tensor: 输入序列表示 [batch, seq_len, hidden]
        Returns:
            hidden_states: 过滤后的序列表示 [batch, seq_len, hidden]
            user_types: 用户类型分布 [batch, 5]
        """
        batch, seq_len, hidden = input_tensor.shape
        
        # 1. 使用FFT将序列转换到频域
        x = torch.fft.rfft(input_tensor, dim=1, norm='ortho')  # [batch, seq_len//2+1, hidden]
        
        # 2. 用户频谱分类
        user_types, power_spectrum = self.spectrum_classifier(x)  # [batch, 5], [batch, seq_len//2+1]
        
        # 3. 根据用户类型生成自适应滤波器
        # 将用户类型概率分布与各类型滤波器参数相乘
        # [batch, 5, 1] * [5, seq_len//2+1] -> [batch, 5, seq_len//2+1]
        user_specific_filters = torch.matmul(
            user_types.unsqueeze(2), 
            self.filter_adjustments.unsqueeze(0)
        ).squeeze(2)  # [batch, seq_len//2+1]
        
        # 4. 应用基础可学习滤波器
        weight = torch.view_as_complex(self.complex_weight)  # [1, seq_len//2+1, hidden]
        
        # 5. 将用户特定滤波器应用到基础滤波器上
        # [batch, seq_len//2+1, 1] * [1, seq_len//2+1, hidden]
        adjusted_weight = weight * user_specific_filters.unsqueeze(-1)
        
        # 6. 应用调整后的滤波器到频域表示
        x = x * adjusted_weight  # [batch, seq_len//2+1, hidden]
        
        # 7. 使用逆FFT将过滤后的频谱转换回时域
        sequence_emb_fft = torch.fft.irfft(x, n=seq_len, dim=1, norm='ortho')
        
        # 8. 应用dropout和残差连接
        hidden_states = self.out_dropout(sequence_emb_fft)
        hidden_states = self.LayerNorm(hidden_states + input_tensor)
        
        return hidden_states, user_types

class FilterLayer(nn.Module):
    """原始滤波器层（保留兼容性）"""
    def __init__(self, args):
        super(FilterLayer, self).__init__()
        # 做完self-attention 做一个前馈全连接 LayerNorm 输出
        self.complex_weight = nn.Parameter(torch.randn(1, args.max_seq_length//2 + 1, args.hidden_size, 2, dtype=torch.float32) * 0.02)
        self.out_dropout = nn.Dropout(args.hidden_dropout_prob)
        self.LayerNorm = LayerNorm(args.hidden_size, eps=1e-12)

    def forward(self, input_tensor):
        # [batch, seq_len, hidden]
        batch, seq_len, hidden = input_tensor.shape
        x = torch.fft.rfft(input_tensor, dim=1, norm='ortho')
        weight = torch.view_as_complex(self.complex_weight)
        x = x * weight
        sequence_emb_fft = torch.fft.irfft(x, n=seq_len, dim=1, norm='ortho')
        hidden_states = self.out_dropout(sequence_emb_fft)
        hidden_states = self.LayerNorm(hidden_states + input_tensor)

        return hidden_states

class Intermediate(nn.Module):
    def __init__(self, args):
        super(Intermediate, self).__init__()
        self.dense_1 = nn.Linear(args.hidden_size, args.hidden_size * 4)
        if isinstance(args.hidden_act, str):
            self.intermediate_act_fn = ACT2FN[args.hidden_act]
        else:
            self.intermediate_act_fn = args.hidden_act

        self.dense_2 = nn.Linear(4 * args.hidden_size, args.hidden_size)
        self.LayerNorm = LayerNorm(args.hidden_size, eps=1e-12)
        self.dropout = nn.Dropout(args.hidden_dropout_prob)

    def forward(self, input_tensor):
        hidden_states = self.dense_1(input_tensor)
        hidden_states = self.intermediate_act_fn(hidden_states)

        hidden_states = self.dense_2(hidden_states)
        hidden_states = self.dropout(hidden_states)
        hidden_states = self.LayerNorm(hidden_states + input_tensor)

        return hidden_states

class Layer(nn.Module):
    def __init__(self, args):
        super(Layer, self).__init__()
        self.no_filters = args.no_filters
        self.use_adaptive_filter = args.use_adaptive_filter if hasattr(args, 'use_adaptive_filter') else False
        
        if self.no_filters:
            self.attention = SelfAttention(args)
        elif self.use_adaptive_filter:
            self.filterlayer = AdaptiveFilterLayer(args)
        else:
            self.filterlayer = FilterLayer(args)
            
        self.intermediate = Intermediate(args)

    def forward(self, hidden_states, attention_mask):
        if self.no_filters:
            hidden_states = self.attention(hidden_states, attention_mask)
            user_types = None
        elif self.use_adaptive_filter:
            hidden_states, user_types = self.filterlayer(hidden_states)
        else:
            hidden_states = self.filterlayer(hidden_states)
            user_types = None

        intermediate_output = self.intermediate(hidden_states)
        return intermediate_output, user_types

class Encoder(nn.Module):
    def __init__(self, args):
        super(Encoder, self).__init__()
        layer = Layer(args)
        self.layer = nn.ModuleList([copy.deepcopy(layer)
                                    for _ in range(args.num_hidden_layers)])
        self.use_adaptive_filter = args.use_adaptive_filter if hasattr(args, 'use_adaptive_filter') else False

    def forward(self, hidden_states, attention_mask, output_all_encoded_layers=True):
        all_encoder_layers = []
        all_user_types = []
        
        for layer_module in self.layer:
            hidden_states, user_types = layer_module(hidden_states, attention_mask)
            if output_all_encoded_layers:
                all_encoder_layers.append(hidden_states)
                if self.use_adaptive_filter:
                    all_user_types.append(user_types)
                    
        if not output_all_encoded_layers:
            all_encoder_layers.append(hidden_states)
            if self.use_adaptive_filter:
                all_user_types.append(user_types)
                
        if self.use_adaptive_filter:
            return all_encoder_layers, all_user_types
        else:
            return all_encoder_layers
