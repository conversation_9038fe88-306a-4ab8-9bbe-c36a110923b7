# 增强型FMLP-Rec：用户频谱分析与公平性感知学习

本项目通过引入用户频谱分析、自适应过滤机制和公平性感知的物品表示学习，扩展了原始的FMLP-Rec模型。增强型模型解决了三个关键限制：

1. 不同用户具有不同的兴趣模式，统一的序列编码器和数据增强可能会削弱个体兴趣偏好
2. 序列编码器具有内在的架构偏差，倾向于关注用户行为序列的特定频率范围
3. 推荐系统需要平衡流行度偏差与长尾物品的公平曝光

## 框架组件

### 1. 用户频谱分析器
- 将用户分类为不同的频谱类别：
  - 低通滤波器(LPF)用户：稳定的长期兴趣，偏好变化缓慢
  - 高通滤波器(HPF)用户：快速变化的偏好，容易受流行物品影响
  - 带通滤波器(BPF)用户：周期性行为模式
  - 带阻滤波器(BSF)用户：兴趣发展不连续，存在显著跳跃
  - 全通滤波器(APF)用户：稳定与探索行为的平衡混合
- 分析流行度偏好：
  - 平均流行度
  - 流行度方差
  - 流行度熵
  - 流行度分布

### 2. 自适应频谱过滤器
- 根据用户特性动态调整过滤机制
- 对于LPF用户：保留低频成分，减少偶发交互的噪声
- 对于HPF用户：增强高频成分，关注最近的兴趣变化
- 对于BPF用户：选择性保留匹配周期模式的特定频带
- 对于BSF用户：增强低频和高频，同时抑制中频
- 对于APF用户：应用轻微扰动以鼓励多样性

### 3. 物品表示学习
- 物品表示的多视角融合：
  - 基础ID嵌入
  - 流行度感知嵌入
  - 频域上下文嵌入
- 增强表示的对比学习框架
- 促进长尾物品曝光的公平性技术

### 4. 流行度感知推荐
- 根据用户流行度偏好调整相似度计算
- 平衡推荐准确性与公平曝光

## 运行模型

### 要求
- PyTorch >= 1.7.0
- NumPy
- Matplotlib (用于可视化)
- tqdm

### 训练模型
训练增强型模型：

```bash
python enhanced_main.py --data_name Beauty --full_sort --contrastive_weight 0.1 --fairness_weight 0.05
```

参数：
- `--data_name`：数据集名称（默认：Beauty）
- `--contrastive_weight`：对比学习损失权重（默认：0.1）
- `--fairness_weight`：公平性促进损失权重（默认：0.05）
- `--full_sort`：启用全排序评估
- `--batch_size`：批次大小（默认：256）
- `--hidden_size`：隐藏层大小（默认：64）

### 评估
评估已训练的模型：

```bash
python enhanced_main.py --data_name Beauty --do_eval --load_model 模型名称 --full_sort
```

其中`模型名称`是保存的模型名称（不带.pt扩展名）

### 可视化用户频谱特征

训练模型后，可以可视化用户频谱特征：

```bash
python visualize_spectrum.py --data_name Beauty --load_model 模型名称
```

这将在`output/visualizations`目录中生成可视化，显示：
- 用户类别分布
- 频谱特征分布
- 流行度特征分布
- 频谱和流行度特征之间的关系

## 实现细节

模型通过以下文件实现：
- `enhanced_models.py`：主要模型实现
- `user_spectrum.py`：用户频谱分析模块
- `adaptive_filter.py`：自适应频谱过滤模块
- `item_representation.py`：流行度感知物品表示学习
- `enhanced_trainers.py`：训练过程
- `enhanced_main.py`：训练和评估的主脚本
- `visualize_spectrum.py`：可视化工具

## 评估指标

模型报告标准推荐指标：
- 命中率（HR@5, HR@10, HR@20）
- NDCG（NDCG@5, NDCG@10, NDCG@20）
- MRR（平均倒数排名）

额外的公平性指标：
- 曝光基尼系数：衡量物品曝光不平等性
- 长尾物品曝光：长尾物品的推荐百分比
- 流行度-曝光相关性：物品流行度与曝光之间的相关性 