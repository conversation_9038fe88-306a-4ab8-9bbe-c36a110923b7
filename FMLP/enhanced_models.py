import torch
import torch.nn as nn
import numpy as np
from modules import Encoder, LayerNorm
from user_spectrum import UserSpectralAnalyzer
from adaptive_filter import AdaptiveSpectralFilter
from item_representation import ItemRepresentationLearning

class EnhancedFMLPRecModel(nn.Module):
    """
    增强版FMLP-Rec模型，结合了用户频谱分析、自适应过滤和
    流行度感知的物品表示学习。
    """
    def __init__(self, args):
        super(EnhancedFMLPRecModel, self).__init__()
        self.args = args
        
        # 来自原始FMLP-Rec的基础组件
        self.item_embeddings = nn.Embedding(args.item_size, args.hidden_size, padding_idx=0)
        self.position_embeddings = nn.Embedding(args.max_seq_length, args.hidden_size)
        self.LayerNorm = LayerNorm(args.hidden_size, eps=1e-12)
        self.dropout = nn.Dropout(args.hidden_dropout_prob)
        self.item_encoder = Encoder(args)
        
        # 新组件
        # 1. 用户频谱分析器
        self.user_spectral_analyzer = UserSpectralAnalyzer(args)
        
        # 2. 自适应频谱过滤器
        self.adaptive_filter = AdaptiveSpectralFilter(args)
        
        # 3. 物品表示学习
        self.item_representation = ItemRepresentationLearning(args)
        self.item_representation.set_id_embedding(self.item_embeddings)
        
        # 4. 流行度感知相似度计算
        self.pop_similarity_layer = nn.Sequential(
            nn.Linear(args.hidden_size * 2, args.hidden_size),
            nn.ReLU(),
            nn.Linear(args.hidden_size, 1),
            nn.Sigmoid()
        )
        
        # 物品流行度分数（将在训练期间计算）
        self.register_buffer('item_popularity', torch.zeros(args.item_size))
        
        self.apply(self.init_weights)
        
    def init_weights(self, module):
        """初始化权重"""
        if isinstance(module, (nn.Linear, nn.Embedding)):
            module.weight.data.normal_(mean=0.0, std=self.args.initializer_range)
        elif isinstance(module, LayerNorm):
            module.bias.data.zero_()
            module.weight.data.fill_(1.0)
        if isinstance(module, nn.Linear) and module.bias is not None:
            module.bias.data.zero_()
            
    def update_item_popularity(self, user_sequences):
        """
        根据用户序列中的频率更新物品流行度
        user_sequences: 用户交互序列列表
        """
        # 计算物品出现次数
        item_counts = torch.zeros(self.args.item_size, device=self.item_popularity.device)
        
        for sequence in user_sequences:
            unique_items = torch.unique(sequence)
            for item in unique_items:
                if item > 0:  # 跳过填充物品(0)
                    item_counts[item] += 1
                    
        # 使用指数平滑更新流行度分数
        alpha = 0.9  # 平滑因子
        self.item_popularity = alpha * self.item_popularity + (1 - alpha) * item_counts
        
        # 归一化流行度分数
        if torch.sum(self.item_popularity) > 0:
            self.item_popularity = self.item_popularity / torch.sum(self.item_popularity)
            
    def add_position_embedding(self, sequence):
        """为物品嵌入添加位置嵌入"""
        seq_length = sequence.size(1)
        position_ids = torch.arange(seq_length, dtype=torch.long, device=sequence.device)
        position_ids = position_ids.unsqueeze(0).expand_as(sequence)
        item_embeddings = self.item_embeddings(sequence)
        position_embeddings = self.position_embeddings(position_ids)
        sequence_emb = item_embeddings + position_embeddings
        sequence_emb = self.LayerNorm(sequence_emb)
        sequence_emb = self.dropout(sequence_emb)

        return sequence_emb
    
    def forward(self, input_ids, calculate_loss=False):
        attention_mask = (input_ids > 0).long()
        extended_attention_mask = attention_mask.unsqueeze(1).unsqueeze(2)
        max_len = attention_mask.size(-1)
        attn_shape = (1, max_len, max_len)
        subsequent_mask = torch.triu(torch.ones(attn_shape), diagonal=1)
        subsequent_mask = (subsequent_mask == 0).unsqueeze(1)
        subsequent_mask = subsequent_mask.long()

        if self.args.cuda_condition:
            subsequent_mask = subsequent_mask.cuda()
        extended_attention_mask = extended_attention_mask * subsequent_mask
        extended_attention_mask = extended_attention_mask.to(dtype=next(self.parameters()).dtype)
        extended_attention_mask = (1.0 - extended_attention_mask) * -10000.0

        # 步骤1：获取带有位置信息的基本序列嵌入
        sequence_emb = self.add_position_embedding(input_ids)
        
        # 步骤2：分析用户频谱特性
        user_repr, user_class_probs = self.user_spectral_analyzer(sequence_emb, input_ids, self.item_popularity)
        
        # 步骤3：根据用户类别应用自适应频谱过滤
        filtered_sequence = self.adaptive_filter(sequence_emb, user_class_probs)
        
        # 步骤4：通过编码器传递
        item_encoded_layers = self.item_encoder(filtered_sequence,
                                              extended_attention_mask,
                                              output_all_encoded_layers=True)
        sequence_output = item_encoded_layers[-1]
        
        # 多任务学习的额外损失
        additional_losses = {}
        
        # 步骤5：提取序列表示用于推荐
        seq_repr = sequence_output[:, -1, :]  # [batch, hidden_size]
        
        # 将用户表示添加到序列表示
        enhanced_seq_repr = seq_repr + user_repr  # [batch, hidden_size]
        
        return enhanced_seq_repr, user_class_probs, additional_losses
    
    def predict_item_scores(self, user_repr, user_class_probs, items=None):
        """
        计算流行度感知的推荐分数
        user_repr: [batch, hidden_size] - 用户表示
        user_class_probs: [batch, 5] - 用户过滤器类别概率
        items: 要评分的物品ID，如果为None则评分所有物品
        """
        batch_size = user_repr.size(0)
        
        if items is None:
            # 为所有物品评分
            # 提取增强的物品嵌入
            all_items = torch.arange(1, self.args.item_size, device=user_repr.device)
            item_embs, _ = self.item_representation(all_items, item_popularity=self.item_popularity)
            
            # 计算基本内容相似度
            scores = torch.matmul(user_repr, item_embs.transpose(0, 1))  # [batch, item_size-1]
            
            # 获取用户流行度偏好
            # 将用户表示与每个类别概率连接
            user_pop_pref = torch.cat([user_class_probs, 
                                     torch.mean(user_class_probs, dim=1, keepdim=True)], dim=1)  # [batch, 6]
            
            # 应用流行度调整 - 重塑以进行广播
            pop_adj = 1.0 + 0.2 * (
                (user_pop_pref[:, 0:1] * (self.item_popularity[1:] < 0.25).float()) +  # LPF用户偏好较不流行
                (user_pop_pref[:, 1:2] * (self.item_popularity[1:] > 0.75).float()) +  # HPF用户偏好流行
                (user_pop_pref[:, 4:5] * 0.5)  # APF用户获得轻微多样性提升
            )
            
            # 应用流行度调整
            adjusted_scores = scores * pop_adj
            
            # 为第一个物品（索引0）填充0
            padded_scores = torch.cat([torch.zeros(batch_size, 1, device=scores.device), adjusted_scores], dim=1)
            
            return padded_scores
        else:
            # 为特定物品评分
            item_embs, _ = self.item_representation(items, item_popularity=self.item_popularity)
            
            # 计算内容相似度
            scores = torch.sum(user_repr.unsqueeze(1) * item_embs, dim=2)  # [batch, items_len]
            
            # 计算流行度调整因子
            user_pop_features = torch.cat([
                user_class_probs[:, 0].unsqueeze(1),  # LPF概率
                user_class_probs[:, 1].unsqueeze(1),  # HPF概率
                user_class_probs[:, 4].unsqueeze(1),  # APF概率
            ], dim=1).unsqueeze(1)  # [batch, 1, 3]
            
            item_pop_values = self.item_popularity[items].unsqueeze(2)  # [batch, items_len, 1]
            
            # 根据用户类别应用简单流行度调整
            pop_factors = torch.cat([
                (item_pop_values < 0.25).float(),  # 对于LPF用户（偏好较不流行）
                (item_pop_values > 0.75).float(),  # 对于HPF用户（偏好流行）
                torch.ones_like(item_pop_values) * 0.5,  # 对于APF用户（多样性提升）
            ], dim=2)  # [batch, items_len, 3]
            
            pop_adj = 1.0 + 0.2 * torch.sum(user_pop_features * pop_factors, dim=2)  # [batch, items_len]
            
            # 应用流行度调整
            adjusted_scores = scores * pop_adj
            
            return adjusted_scores 