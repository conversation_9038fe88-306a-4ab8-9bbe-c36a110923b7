import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import math


class UserSpectralAnalyzer(nn.Module):
    """
    用户频谱分析模块，根据用户的频域特性和流行度偏好对用户进行分类
    """
    def __init__(self, args):
        super(UserSpectralAnalyzer, self).__init__()
        self.args = args
        self.hidden_size = args.hidden_size
        
        # 用户频谱分类器
        self.spectral_classifier = nn.Sequential(
            nn.Linear(args.hidden_size, args.hidden_size),
            nn.ReLU(),
            nn.Linear(args.hidden_size, 5)  # 5个类别: LPF, HPF, BPF, BSF, APF
        )
        
        # Popularity preference encoder
        self.pop_encoder = nn.Sequential(
            nn.Linear(4, args.hidden_size//2),  # 4个流行度特征
            nn.ReLU(),
            nn.Linear(args.hidden_size//2, args.hidden_size)
        )
        
        # 用于组合频谱和流行度信息
        self.fusion_layer = nn.Linear(args.hidden_size * 2, args.hidden_size)
        
    def analyze_spectrum(self, sequence_emb):
        """
        分析用户序列的频谱特性
        sequence_emb: [batch, seq_len, hidden_size]
        """
        batch, seq_len, hidden = sequence_emb.shape
        
        # 应用FFT转换到频域
        seq_fft = torch.fft.rfft(sequence_emb, dim=1, norm='ortho')
        
        # 获取幅度谱
        magnitude = torch.abs(seq_fft)
        
        # 计算总能量
        total_energy = torch.sum(magnitude**2, dim=1)  # [batch, hidden]
        
        # 计算低/高频能量
        low_freq_idx = seq_len // 4
        low_freq_energy = torch.sum(magnitude[:, :low_freq_idx, :]**2, dim=1)  # [batch, hidden]
        high_freq_energy = torch.sum(magnitude[:, low_freq_idx:, :]**2, dim=1)  # [batch, hidden]
        
        # 能量比率特征
        low_freq_ratio = low_freq_energy / (total_energy + 1e-8)  # [batch, hidden]
        high_freq_ratio = high_freq_energy / (total_energy + 1e-8)  # [batch, hidden]
        
        # 频谱平坦度（变化）
        log_magnitude = torch.log(magnitude + 1e-8)
        spectral_flatness = torch.exp(torch.mean(log_magnitude, dim=1)) / (torch.mean(magnitude, dim=1) + 1e-8)
        
        # 组合特征
        spectral_features = torch.cat([
            torch.mean(low_freq_ratio, dim=1, keepdim=True),
            torch.mean(high_freq_ratio, dim=1, keepdim=True),
            torch.mean(spectral_flatness, dim=1, keepdim=True)
        ], dim=1)
        
        # 在隐藏维度上平均以获取序列表示
        seq_repr = torch.mean(magnitude, dim=2)  # [batch, seq_len/2+1]
        
        return seq_repr, spectral_features
    
    def classify_user(self, sequence_emb):
        """
        将用户分类为不同的滤波器类型
        返回5个类别的概率: LPF, HPF, BPF, BSF, APF
        """
        seq_repr, _ = self.analyze_spectrum(sequence_emb)
        
        # 通过平均池化创建统一表示
        user_repr = torch.mean(sequence_emb, dim=1)  # [batch, hidden]
        
        # 获取用户类别概率
        user_class_logits = self.spectral_classifier(user_repr)
        user_class_probs = F.softmax(user_class_logits, dim=1)
        
        return user_class_probs
    
    def calculate_popularity_features(self, sequence, item_popularity):
        """
        计算每个用户的流行度特征
        sequence: [batch, seq_len] - 用户交互序列
        item_popularity: [item_size] - 每个物品的流行度分数
        """
        batch_size, seq_len = sequence.shape
        
        # 获取序列中每个物品的流行度
        # 对于填充物品(0)，设置流行度为0
        seq_popularity = item_popularity[sequence]  # [batch, seq_len]
        mask = (sequence > 0).float()  # [batch, seq_len]
        
        # 计算平均流行度
        avg_pop = torch.sum(seq_popularity * mask, dim=1) / (torch.sum(mask, dim=1) + 1e-8)  # [batch]
        
        # 计算流行度方差
        pop_variance = torch.sum(mask * (seq_popularity - avg_pop.unsqueeze(1))**2, dim=1) / (torch.sum(mask, dim=1) + 1e-8)  # [batch]
        
        # 计算流行度熵
        normalized_pop = seq_popularity / (torch.sum(seq_popularity, dim=1, keepdim=True) + 1e-8)
        pop_entropy = -torch.sum(mask * normalized_pop * torch.log(normalized_pop + 1e-8), dim=1)  # [batch]
        
        # 分箱流行度分布（3个箱：低、中、高）
        pop_max = torch.max(item_popularity)
        bin_boundaries = [pop_max/3, 2*pop_max/3]
        
        low_bin = torch.sum(mask * (seq_popularity < bin_boundaries[0]).float(), dim=1) / (torch.sum(mask, dim=1) + 1e-8)
        mid_bin = torch.sum(mask * ((seq_popularity >= bin_boundaries[0]) & (seq_popularity < bin_boundaries[1])).float(), dim=1) / (torch.sum(mask, dim=1) + 1e-8)
        high_bin = torch.sum(mask * (seq_popularity >= bin_boundaries[1]).float(), dim=1) / (torch.sum(mask, dim=1) + 1e-8)
        
        # 组合特征
        pop_features = torch.stack([avg_pop, torch.sqrt(pop_variance), pop_entropy, high_bin], dim=1)
        
        return pop_features
    
    def forward(self, sequence_emb, sequence, item_popularity):
        """
        分析用户序列并生成包含频谱和流行度信息的用户表示
        sequence_emb: [batch, seq_len, hidden_size] - 嵌入后的序列
        sequence: [batch, seq_len] - 原始序列ID
        item_popularity: [item_size] - 每个物品的流行度分数
        """
        # 获取用户频谱类别概率
        user_class_probs = self.classify_user(sequence_emb)
        
        # 计算流行度特征
        pop_features = self.calculate_popularity_features(sequence, item_popularity)
        
        # 编码流行度特征
        pop_embedding = self.pop_encoder(pop_features)
        
        # 从序列获取用户表示
        user_repr = torch.mean(sequence_emb, dim=1)  # [batch, hidden]
        
        # 结合用户表示和流行度信息
        combined_repr = torch.cat([user_repr, pop_embedding], dim=1)
        enhanced_user_repr = self.fusion_layer(combined_repr)
        
        return enhanced_user_repr, user_class_probs 