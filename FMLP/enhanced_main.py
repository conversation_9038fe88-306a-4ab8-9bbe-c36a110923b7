import os
import torch
import argparse
import numpy as np

from enhanced_models import EnhancedFMLPRecModel
from enhanced_trainers import EnhancedFMLPRecTrainer
from utils import EarlyStopping, check_path, set_seed, get_local_time, get_seq_dic, get_dataloder, get_rating_matrix

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_dir", default="./data/", type=str)
    parser.add_argument("--output_dir", default="output/", type=str)
    parser.add_argument("--data_name", default="Beauty", type=str)
    parser.add_argument("--do_eval", action="store_true")
    parser.add_argument("--load_model", default=None, type=str)

    # 模型参数
    parser.add_argument("--model_name", default="EnhancedFMLPRec", type=str)
    parser.add_argument("--hidden_size", default=64, type=int, help="模型隐藏层大小")
    parser.add_argument("--num_hidden_layers", default=2, type=int, help="滤波器增强块的数量")
    parser.add_argument("--num_attention_heads", default=2, type=int)
    parser.add_argument("--hidden_act", default="gelu", type=str)
    parser.add_argument("--attention_probs_dropout_prob", default=0.5, type=float)
    parser.add_argument("--hidden_dropout_prob", default=0.5, type=float)
    parser.add_argument("--initializer_range", default=0.02, type=float)
    parser.add_argument("--max_seq_length", default=50, type=int)
    parser.add_argument("--no_filters", action="store_true", help="如果无滤波器，滤波层转换为自注意力")

    # 增强模型参数
    parser.add_argument("--contrastive_weight", default=0.1, type=float, help="对比学习损失的权重")
    parser.add_argument("--fairness_weight", default=0.05, type=float, help="公平性促进损失的权重")
    parser.add_argument("--use_popularity", default=True, type=bool, help="是否使用流行度感知学习")

    # 训练参数
    parser.add_argument("--lr", default=0.001, type=float, help="Adam学习率")
    parser.add_argument("--batch_size", default=256, type=int, help="批次大小")
    parser.add_argument("--epochs", default=200, type=int, help="训练轮数")
    parser.add_argument("--no_cuda", action="store_true")
    parser.add_argument("--log_freq", default=1, type=int, help="每轮打印结果的频率")
    parser.add_argument("--full_sort", action="store_true")
    parser.add_argument("--patience", default=10, type=int, help="验证损失未改善后等待多久停止")

    parser.add_argument("--seed", default=42, type=int)
    parser.add_argument("--weight_decay", default=0.0, type=float, help="Adam权重衰减")
    parser.add_argument("--adam_beta1", default=0.9, type=float, help="Adam第一个beta值")
    parser.add_argument("--adam_beta2", default=0.999, type=float, help="Adam第二个beta值")
    parser.add_argument("--gpu_id", default="0", type=str, help="GPU ID")
    parser.add_argument("--variance", default=5, type=float)

    args = parser.parse_args()

    set_seed(args.seed)
    check_path(args.output_dir)

    os.environ["CUDA_VISIBLE_DEVICES"] = args.gpu_id
    args.cuda_condition = torch.cuda.is_available() and not args.no_cuda

    seq_dic, max_item = get_seq_dic(args)

    args.item_size = max_item + 1

    # 保存模型参数
    cur_time = get_local_time()
    args_str = f'{args.model_name}-{args.data_name}-{cur_time}'
    args.log_file = os.path.join(args.output_dir, args_str + '.txt')
    print(str(args))
    with open(args.log_file, 'a') as f:
        f.write(str(args) + '\n')

    # 保存模型
    args.checkpoint_path = os.path.join(args.output_dir, args_str + '.pt')

    train_dataloader, eval_dataloader, test_dataloader = get_dataloder(args, seq_dic)

    # 初始化增强模型和训练器
    model = EnhancedFMLPRecModel(args=args)
    trainer = EnhancedFMLPRecTrainer(model, train_dataloader, eval_dataloader,
                              test_dataloader, args)

    if args.full_sort:
        args.valid_rating_matrix, args.test_rating_matrix = get_rating_matrix(args.data_name, seq_dic, max_item)

    if args.do_eval:
        if args.load_model is None:
            print(f"没有输入模型！")
            exit(0)
        else:
            args.checkpoint_path = os.path.join(args.output_dir, args.load_model + '.pt')
            trainer.load(args.checkpoint_path)
            print(f"从 {args.checkpoint_path} 加载模型进行测试！")
            scores, result_info = trainer.test(0, full_sort=args.full_sort)
            
            # 同时计算公平性指标
            if args.full_sort:
                fairness_metrics = calculate_fairness_metrics(trainer, test_dataloader, args)
                print(fairness_metrics)
                with open(args.log_file, 'a') as f:
                    f.write(str(fairness_metrics) + '\n')

    else:
        early_stopping = EarlyStopping(args.checkpoint_path, patience=args.patience, verbose=True)
        for epoch in range(args.epochs):
            trainer.train(epoch)
            scores, _ = trainer.valid(epoch, full_sort=args.full_sort)
            # 使用MRR评估
            early_stopping(np.array(scores[-1:]), trainer.model)
            if early_stopping.early_stop:
                print("提前停止")
                break

        print("---------------最终结果---------------")
        # 加载最佳模型
        trainer.model.load_state_dict(torch.load(args.checkpoint_path))
        scores, result_info = trainer.test(0, full_sort=args.full_sort)
        
        # 计算公平性指标
        if args.full_sort:
            fairness_metrics = calculate_fairness_metrics(trainer, test_dataloader, args)
            print(fairness_metrics)
            with open(args.log_file, 'a') as f:
                f.write(str(fairness_metrics) + '\n')

    print(args_str)
    print(result_info)
    with open(args.log_file, 'a') as f:
        f.write(args_str + '\n')
        f.write(result_info + '\n')


def calculate_fairness_metrics(trainer, dataloader, args):
    """
    计算公平性指标，如曝光公平性和流行度多样性
    """
    trainer.model.eval()
    
    # 获取物品流行度
    item_popularity = trainer.model.item_popularity.cpu().numpy()
    
    # 初始化跟踪变量
    all_item_exposure = np.zeros(args.item_size)
    total_predictions = 0
    
    # 计算推荐物品分布
    for i, batch in enumerate(dataloader):
        batch = tuple(t.to(trainer.device) for t in batch)
        user_ids, input_ids, answers, _, _ = batch
        
        # 获取用户表示
        with torch.no_grad():
            user_repr, user_class_probs, _ = trainer.model(input_ids)
            rating_pred = trainer.predict_full(user_repr, user_class_probs)
            
        # 获取前20个推荐
        rating_pred = rating_pred.cpu().numpy()
        top_items = np.argpartition(rating_pred, -20)[:, -20:]
        
        # 更新曝光计数
        for user_idx in range(len(top_items)):
            for item in top_items[user_idx]:
                all_item_exposure[item] += 1
                total_predictions += 1
    
    # 归一化曝光
    if total_predictions > 0:
        all_item_exposure = all_item_exposure / total_predictions
    
    # 计算指标
    # 1. 基尼系数（曝光不平等性）
    sorted_exposure = np.sort(all_item_exposure)
    n = len(sorted_exposure)
    exposure_gini = np.sum(np.abs(np.subtract.outer(sorted_exposure, sorted_exposure))) / (2 * n**2 * np.mean(sorted_exposure))
    
    # 2. 长尾物品曝光（按流行度排名后80%的物品）
    popularity_threshold = np.percentile(item_popularity[item_popularity > 0], 80)
    long_tail_items = item_popularity <= popularity_threshold
    long_tail_exposure = np.sum(all_item_exposure[long_tail_items]) / np.sum(all_item_exposure)
    
    # 3. 流行度多样性（物品流行度与曝光之间的相关性）
    valid_items = (item_popularity > 0) & (all_item_exposure > 0)
    if np.sum(valid_items) > 1:
        popularity_exposure_correlation = np.corrcoef(item_popularity[valid_items], all_item_exposure[valid_items])[0, 1]
    else:
        popularity_exposure_correlation = 0.0
    
    return {
        "exposure_gini": exposure_gini,
        "long_tail_exposure": long_tail_exposure,
        "popularity_exposure_correlation": popularity_exposure_correlation
    }


if __name__ == "__main__":
    main() 