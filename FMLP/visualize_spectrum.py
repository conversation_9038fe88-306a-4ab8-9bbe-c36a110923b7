import torch
import numpy as np
import matplotlib.pyplot as plt
import os
import argparse
from tqdm import tqdm

from enhanced_models import EnhancedFMLPRecModel
from utils import set_seed, get_seq_dic, get_dataloder


def visualize_user_spectrum(args):
    """
    从训练模型中可视化用户频谱特征
    """
    # 设置设备
    args.cuda_condition = torch.cuda.is_available() and not args.no_cuda
    device = torch.device("cuda" if args.cuda_condition else "cpu")
    
    # 加载数据
    seq_dic, max_item = get_seq_dic(args)
    args.item_size = max_item + 1
    _, _, test_dataloader = get_dataloder(args, seq_dic)
    
    # 加载模型
    model = EnhancedFMLPRecModel(args=args)
    if args.cuda_condition:
        model.cuda()
    
    # 加载模型参数
    checkpoint_path = os.path.join(args.output_dir, args.load_model + '.pt')
    model.load_state_dict(torch.load(checkpoint_path, map_location=device))
    model.eval()
    
    # 跟踪用户类别和频谱特征
    user_classes = []
    spectral_features = []
    popularity_features = []
    
    # 处理批次
    print("处理用户序列...")
    for batch in tqdm(test_dataloader):
        batch = tuple(t.to(device) for t in batch)
        user_ids, input_ids, _, _, _ = batch
        
        # 获取序列嵌入
        with torch.no_grad():
            sequence_emb = model.add_position_embedding(input_ids)
            
            # 获取用户频谱特征
            seq_repr, spectral_feat = model.user_spectral_analyzer.analyze_spectrum(sequence_emb)
            user_class_probs = model.user_spectral_analyzer.classify_user(sequence_emb)
            
            # 获取流行度特征
            pop_features = model.user_spectral_analyzer.calculate_popularity_features(
                input_ids, model.item_popularity
            )
            
        # 存储结果
        user_classes.append(user_class_probs.cpu().numpy())
        spectral_features.append(spectral_feat.cpu().numpy())
        popularity_features.append(pop_features.cpu().numpy())
    
    # 连接结果
    user_classes = np.concatenate(user_classes, axis=0)
    spectral_features = np.concatenate(spectral_features, axis=0)
    popularity_features = np.concatenate(popularity_features, axis=0)
    
    # 如果不存在，创建输出目录
    viz_dir = os.path.join(args.output_dir, "visualizations")
    os.makedirs(viz_dir, exist_ok=True)
    
    # 绘制用户类别分布
    plot_user_class_distribution(user_classes, viz_dir)
    
    # 绘制频谱特征分布
    plot_spectral_features(spectral_features, viz_dir)
    
    # 绘制流行度特征
    plot_popularity_features(popularity_features, viz_dir)
    
    # 绘制频谱和流行度特征之间的关系
    plot_spectral_popularity_relationship(spectral_features, popularity_features, user_classes, viz_dir)
    

def plot_user_class_distribution(user_classes, viz_dir):
    """绘制用户类别分布"""
    class_names = ['LPF', 'HPF', 'BPF', 'BSF', 'APF']
    
    # 获取每个用户最可能的类别
    most_likely_class = np.argmax(user_classes, axis=1)
    class_counts = np.bincount(most_likely_class, minlength=5)
    
    plt.figure(figsize=(10, 6))
    bars = plt.bar(class_names, class_counts)
    
    # 添加百分比标签
    total = class_counts.sum()
    for bar, count in zip(bars, class_counts):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{count} ({count/total*100:.1f}%)', 
                ha='center', va='bottom')
    
    plt.title('用户频谱类别分布')
    plt.xlabel('用户类别')
    plt.ylabel('用户数量')
    plt.savefig(os.path.join(viz_dir, 'user_class_distribution.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 绘制每个类别的概率分布
    plt.figure(figsize=(12, 8))
    for i, class_name in enumerate(class_names):
        plt.subplot(2, 3, i+1)
        plt.hist(user_classes[:, i], bins=20, alpha=0.7)
        plt.title(f'{class_name} 概率分布')
        plt.xlabel('概率')
        plt.ylabel('用户数量')
    
    plt.tight_layout()
    plt.savefig(os.path.join(viz_dir, 'user_class_probabilities.png'), dpi=300, bbox_inches='tight')
    plt.close()


def plot_spectral_features(spectral_features, viz_dir):
    """绘制频谱特征分布"""
    feature_names = ['低频比例', '高频比例', '频谱平坦度']
    
    plt.figure(figsize=(15, 5))
    for i, name in enumerate(feature_names):
        plt.subplot(1, 3, i+1)
        plt.hist(spectral_features[:, i], bins=20, alpha=0.7)
        plt.title(f'{name}分布')
        plt.xlabel(name)
        plt.ylabel('用户数量')
    
    plt.tight_layout()
    plt.savefig(os.path.join(viz_dir, 'spectral_features.png'), dpi=300, bbox_inches='tight')
    plt.close()


def plot_popularity_features(popularity_features, viz_dir):
    """绘制流行度特征分布"""
    feature_names = ['平均流行度', '流行度方差', '流行度熵', '高流行度比例']
    
    plt.figure(figsize=(15, 10))
    for i, name in enumerate(feature_names):
        plt.subplot(2, 2, i+1)
        plt.hist(popularity_features[:, i], bins=20, alpha=0.7)
        plt.title(f'{name}分布')
        plt.xlabel(name)
        plt.ylabel('用户数量')
    
    plt.tight_layout()
    plt.savefig(os.path.join(viz_dir, 'popularity_features.png'), dpi=300, bbox_inches='tight')
    plt.close()


def plot_spectral_popularity_relationship(spectral_features, popularity_features, user_classes, viz_dir):
    """绘制频谱和流行度特征之间的关系"""
    class_names = ['LPF', 'HPF', 'BPF', 'BSF', 'APF']
    most_likely_class = np.argmax(user_classes, axis=1)
    
    # 频谱平坦度与流行度方差
    plt.figure(figsize=(10, 8))
    for i in range(5):
        mask = most_likely_class == i
        if np.sum(mask) > 0:
            plt.scatter(
                spectral_features[mask, 2],  # 频谱平坦度
                popularity_features[mask, 1],  # 流行度方差
                alpha=0.7,
                label=class_names[i]
            )
    
    plt.title('频谱平坦度与流行度方差的关系')
    plt.xlabel('频谱平坦度')
    plt.ylabel('流行度方差')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(viz_dir, 'spectral_popularity_relationship.png'), dpi=300, bbox_inches='tight')
    plt.close()
    
    # 低频比例与平均流行度
    plt.figure(figsize=(10, 8))
    for i in range(5):
        mask = most_likely_class == i
        if np.sum(mask) > 0:
            plt.scatter(
                spectral_features[mask, 0],  # 低频比例
                popularity_features[mask, 0],  # 平均流行度
                alpha=0.7,
                label=class_names[i]
            )
    
    plt.title('低频比例与平均流行度的关系')
    plt.xlabel('低频比例')
    plt.ylabel('平均流行度')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(os.path.join(viz_dir, 'low_freq_popularity_relationship.png'), dpi=300, bbox_inches='tight')
    plt.close()


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--data_dir", default="./data/", type=str)
    parser.add_argument("--output_dir", default="output/", type=str)
    parser.add_argument("--data_name", default="Beauty", type=str)
    parser.add_argument("--load_model", required=True, type=str, help="要加载的模型名称")
    parser.add_argument("--model_name", default="EnhancedFMLPRec", type=str)
    
    # 模型参数（应与训练模型匹配）
    parser.add_argument("--hidden_size", default=64, type=int)
    parser.add_argument("--num_hidden_layers", default=2, type=int)
    parser.add_argument("--num_attention_heads", default=2, type=int)
    parser.add_argument("--hidden_act", default="gelu", type=str)
    parser.add_argument("--max_seq_length", default=50, type=int)
    parser.add_argument("--no_filters", action="store_true")
    parser.add_argument("--no_cuda", action="store_true")
    parser.add_argument("--batch_size", default=128, type=int)
    parser.add_argument("--seed", default=42, type=int)
    
    args = parser.parse_args()
    set_seed(args.seed)
    
    os.environ["CUDA_VISIBLE_DEVICES"] = "0"
    
    visualize_user_spectrum(args) 