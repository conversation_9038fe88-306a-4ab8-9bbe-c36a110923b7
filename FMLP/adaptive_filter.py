import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np

class AdaptiveSpectralFilter(nn.Module):
    """
    自适应频谱过滤模块，根据用户频谱特性动态调整过滤过程
    """
    def __init__(self, args):
        super(AdaptiveSpectralFilter, self).__init__()
        self.args = args
        self.hidden_size = args.hidden_size
        
        # 不同用户类型的过滤器参数
        # LPF - 低通滤波器（增强低频）
        self.lpf_params = nn.Parameter(torch.FloatTensor(1, args.max_seq_length//2 + 1).fill_(1.0))
        
        # HPF - 高通滤波器（增强高频）
        self.hpf_params = nn.Parameter(torch.FloatTensor(1, args.max_seq_length//2 + 1).fill_(1.0))
        
        # BPF - 带通滤波器（增强特定频段）
        self.bpf_params = nn.Parameter(torch.FloatTensor(1, args.max_seq_length//2 + 1).fill_(1.0))
        
        # BSF - 带阻滤波器（阻断特定频段）
        self.bsf_params = nn.Parameter(torch.FloatTensor(1, args.max_seq_length//2 + 1).fill_(1.0))
        
        # APF - 全通滤波器（对所有频率进行轻微修改）
        self.apf_params = nn.Parameter(torch.FloatTensor(1, args.max_seq_length//2 + 1).fill_(1.0))
        
        # 初始化滤波器参数
        self._init_filters()
        
        # 门控机制控制滤波强度
        self.gate_network = nn.Sequential(
            nn.Linear(args.hidden_size, args.hidden_size // 2),
            nn.ReLU(),
            nn.Linear(args.hidden_size // 2, 1),
            nn.Sigmoid()
        )
        
    def _init_filters(self):
        """使用适当的频率响应初始化滤波器参数"""
        seq_len = self.args.max_seq_length
        half_len = seq_len // 2 + 1
        
        # 低通滤波器 - 逐渐减弱高频分量
        lpf_curve = torch.exp(-torch.arange(half_len) * (4.0 / half_len))
        self.lpf_params.data = lpf_curve.unsqueeze(0)
        
        # 高通滤波器 - 逐渐增强高频分量
        hpf_curve = 1 - torch.exp(-torch.arange(half_len) * (4.0 / half_len))
        self.hpf_params.data = hpf_curve.unsqueeze(0)
        
        # 带通滤波器 - 增强中频
        center = half_len // 2
        width = half_len // 4
        x = torch.arange(half_len)
        bpf_curve = torch.exp(-((x - center) ** 2) / (2 * width ** 2))
        self.bpf_params.data = bpf_curve.unsqueeze(0)
        
        # 带阻滤波器 - 减弱中频
        bsf_curve = 1 - torch.exp(-((x - center) ** 2) / (2 * width ** 2))
        self.bsf_params.data = bsf_curve.unsqueeze(0)
        
        # 全通滤波器 - 轻微随机扰动
        apf_curve = torch.ones(half_len) + torch.randn(half_len) * 0.1
        self.apf_params.data = apf_curve.unsqueeze(0)
        
    def apply_filter(self, x_fft, user_class_probs):
        """
        根据用户分类应用适当的滤波器
        x_fft: [batch, seq_len//2+1, hidden] - 频域表示
        user_class_probs: [batch, 5] - 每个用户类别的概率 (LPF, HPF, BPF, BSF, APF)
        """
        batch_size = user_class_probs.size(0)
        seq_len = x_fft.size(1)
        hidden = x_fft.size(2)
        
        # 扩展滤波器参数以匹配批次和隐藏维度
        lpf = self.lpf_params.unsqueeze(0).repeat(batch_size, 1, 1)  # [batch, 1, seq_len//2+1]
        hpf = self.hpf_params.unsqueeze(0).repeat(batch_size, 1, 1)
        bpf = self.bpf_params.unsqueeze(0).repeat(batch_size, 1, 1)
        bsf = self.bsf_params.unsqueeze(0).repeat(batch_size, 1, 1)
        apf = self.apf_params.unsqueeze(0).repeat(batch_size, 1, 1)
        
        # 根据类别概率加权应用每个滤波器
        filtered = (lpf * user_class_probs[:, 0:1, None] + 
                   hpf * user_class_probs[:, 1:2, None] +
                   bpf * user_class_probs[:, 2:3, None] +
                   bsf * user_class_probs[:, 3:4, None] +
                   apf * user_class_probs[:, 4:5, None])  # [batch, 1, seq_len//2+1]
        
        # 扩展过滤后的结果以匹配隐藏维度
        filtered = filtered.expand(-1, hidden, -1).transpose(1, 2)  # [batch, seq_len//2+1, hidden]
        
        # 应用自适应滤波器
        return x_fft * filtered
        
    def forward(self, sequence_emb, user_class_probs):
        """
        应用自适应频谱过滤
        sequence_emb: [batch, seq_len, hidden]
        user_class_probs: [batch, 5] - 每种滤波器类型的概率
        """
        batch, seq_len, hidden = sequence_emb.shape
        
        # 根据序列表示计算门控因子
        seq_repr = torch.mean(sequence_emb, dim=1)  # [batch, hidden]
        gate = self.gate_network(seq_repr)  # [batch, 1]
        
        # 转换到频域
        x_fft = torch.fft.rfft(sequence_emb, dim=1, norm='ortho')
        
        # 应用自适应过滤
        filtered_fft = self.apply_filter(x_fft, user_class_probs)
        
        # 转换回时域
        filtered_seq = torch.fft.irfft(filtered_fft, n=seq_len, dim=1, norm='ortho')
        
        # 应用门控以控制过滤强度
        output = gate.unsqueeze(1) * filtered_seq + (1 - gate.unsqueeze(1)) * sequence_emb
        
        return output 