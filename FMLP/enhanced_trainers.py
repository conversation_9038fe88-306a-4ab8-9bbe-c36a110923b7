import torch
import tqdm
import numpy as np
from torch.optim import <PERSON>
from trainers import Trainer
from utils import recall_at_k, ndcg_k, get_metric

class EnhancedFMLPRecTrainer(Trainer):
    """
    增强版FMLP-Rec训练器，结合频谱分析和流行度感知学习
    """
    def __init__(self, model, train_dataloader, eval_dataloader, test_dataloader, args):
        super(EnhancedFMLPRecTrainer, self).__init__(
            model, train_dataloader, eval_dataloader, test_dataloader, args
        )
        
        # 对比学习的超参数
        self.contrastive_weight = args.contrastive_weight if hasattr(args, 'contrastive_weight') else 0.1
        self.fairness_weight = args.fairness_weight if hasattr(args, 'fairness_weight') else 0.05
        
        # 在批次间跟踪频率上下文
        self.freq_contexts = {}
        
        # 初始化流行度跟踪
        self._init_popularity()
        
    def _init_popularity(self):
        """从训练数据初始化物品流行度"""
        print("初始化物品流行度...")
        
        # 收集所有序列
        all_sequences = []
        for batch in self.train_dataloader:
            _, input_ids, _, _ = batch
            all_sequences.append(input_ids)
            
        # 更新模型的物品流行度
        self.model.update_item_popularity(torch.cat(all_sequences, dim=0))
    
    def enhanced_cross_entropy(self, user_repr, user_class_probs, pos_ids, neg_ids):
        """
        增强的交叉熵损失，结合流行度感知评分和对比学习
        """
        # 获取正样本和负样本的物品嵌入
        pos_embs, pos_cl_loss = self.model.item_representation(
            pos_ids, 
            item_popularity=self.model.item_popularity
        )
        
        neg_embs, neg_cl_loss = self.model.item_representation(
            neg_ids, 
            item_popularity=self.model.item_popularity
        )
        
        # 使用流行度感知调整计算分数
        pos_scores = self.model.predict_item_scores(user_repr, user_class_probs, pos_ids)
        neg_scores = self.model.predict_item_scores(user_repr, user_class_probs, neg_ids)
        
        # 二元交叉熵损失
        main_loss = torch.mean(
            - torch.log(torch.sigmoid(pos_scores) + 1e-24) -
            torch.log(1 - torch.sigmoid(neg_scores) + 1e-24)
        )
        
        # 如果可用，添加对比损失
        contrastive_loss = 0
        if pos_cl_loss is not None:
            contrastive_loss += pos_cl_loss
        if neg_cl_loss is not None:
            contrastive_loss += neg_cl_loss
            
        # 计算促进公平性的损失
        # 获取流行度值
        pos_pop = self.model.item_popularity[pos_ids]
        neg_pop = self.model.item_popularity[neg_ids]
        
        # 计算流行度不平衡惩罚
        # 我们希望惩罚模型如果它一致地偏好高流行度物品
        pop_diff = pos_pop - neg_pop
        fairness_loss = torch.mean(torch.clamp(pop_diff, min=0) * torch.sigmoid(pos_scores - neg_scores))
        
        # 总损失
        total_loss = main_loss + self.contrastive_weight * contrastive_loss + self.fairness_weight * fairness_loss
        
        return total_loss, {
            'main_loss': main_loss.item(),
            'contrastive_loss': contrastive_loss.item() if isinstance(contrastive_loss, torch.Tensor) else 0,
            'fairness_loss': fairness_loss.item()
        }
    
    def predict_sample(self, user_repr, user_class_probs, test_neg_sample):
        """
        为基于样本的评估预测分数
        """
        test_logits = self.model.predict_item_scores(user_repr, user_class_probs, test_neg_sample)
        return test_logits
    
    def predict_full(self, user_repr, user_class_probs):
        """
        为全排序评估预测分数
        """
        rating_pred = self.model.predict_item_scores(user_repr, user_class_probs)
        return rating_pred
    
    def iteration(self, epoch, dataloader, full_sort=False, train=True):
        """
        执行一次训练或评估迭代（epoch）
        """
        str_code = "train" if train else "test"
        
        # 设置tqdm进度条
        rec_data_iter = tqdm.tqdm(
            enumerate(dataloader),
            desc="推荐 EP_%s:%d" % (str_code, epoch),
            total=len(dataloader),
            bar_format="{l_bar}{r_bar}"
        )
        
        if train:
            self.model.train()
            rec_loss = 0.0
            loss_components = {'main_loss': 0.0, 'contrastive_loss': 0.0, 'fairness_loss': 0.0}
            
            for i, batch in rec_data_iter:
                # 将批次移至设备
                batch = tuple(t.to(self.device) for t in batch)
                _, input_ids, answer, neg_answer = batch
                
                # 前向传播
                user_repr, user_class_probs, _ = self.model(input_ids)
                
                # 计算损失
                loss, batch_loss_components = self.enhanced_cross_entropy(
                    user_repr, user_class_probs, answer, neg_answer
                )
                
                # 更新模型
                self.optim.zero_grad()
                loss.backward()
                self.optim.step()
                
                # 跟踪损失
                rec_loss += loss.item()
                for k, v in batch_loss_components.items():
                    loss_components[k] += v
                
                # 定期更新物品流行度
                if i % 50 == 0:
                    self.model.update_item_popularity(input_ids)
            
            # 平均损失组件
            for k in loss_components:
                loss_components[k] /= len(rec_data_iter)
            
            post_fix = {
                "epoch": epoch,
                "rec_loss": '{:.4f}'.format(rec_loss / len(rec_data_iter)),
                "main_loss": '{:.4f}'.format(loss_components['main_loss']),
                "contrastive_loss": '{:.4f}'.format(loss_components['contrastive_loss']),
                "fairness_loss": '{:.4f}'.format(loss_components['fairness_loss'])
            }
            
            if (epoch + 1) % self.args.log_freq == 0:
                print(str(post_fix))
                
            with open(self.args.log_file, 'a') as f:
                f.write(str(post_fix) + '\n')
                
        else:
            self.model.eval()
            
            pred_list = None
            
            if full_sort:
                answer_list = None
                for i, batch in rec_data_iter:
                    # 将批次移至设备
                    batch = tuple(t.to(self.device) for t in batch)
                    user_ids, input_ids, answers, _, _ = batch
                    
                    # 获取用户表示
                    with torch.no_grad():
                        user_repr, user_class_probs, _ = self.model(input_ids)
                        rating_pred = self.predict_full(user_repr, user_class_probs)
                        
                    rating_pred = rating_pred.cpu().data.numpy().copy()
                    batch_user_index = user_ids.cpu().numpy()
                    
                    # 将训练数据中出现的物品置零
                    rating_pred[self.args.train_matrix[batch_user_index].toarray() > 0] = 0
                    
                    # 获取前20个物品
                    ind = np.argpartition(rating_pred, -20)[:, -20:]
                    arr_ind = rating_pred[np.arange(len(rating_pred))[:, None], ind]
                    arr_ind_argsort = np.argsort(arr_ind)[np.arange(len(rating_pred)), ::-1]
                    batch_pred_list = ind[np.arange(len(rating_pred))[:, None], arr_ind_argsort]
                    
                    if i == 0:
                        pred_list = batch_pred_list
                        answer_list = answers.cpu().data.numpy()
                    else:
                        pred_list = np.append(pred_list, batch_pred_list, axis=0)
                        answer_list = np.append(answer_list, answers.cpu().data.numpy(), axis=0)
                
                # 计算指标
                return self.get_full_sort_score(epoch, answer_list, pred_list)
                
            else:
                for i, batch in rec_data_iter:
                    # 将批次移至设备
                    batch = tuple(t.to(self.device) for t in batch)
                    user_ids, input_ids, answers, _, sample_negs = batch
                    
                    # 获取用户表示
                    with torch.no_grad():
                        user_repr, user_class_probs, _ = self.model(input_ids)
                        test_neg_items = torch.cat((answers.unsqueeze(-1), sample_negs), -1)
                        test_logits = self.predict_sample(user_repr, user_class_probs, test_neg_items)
                        
                    test_logits = test_logits.cpu().detach().numpy().copy()
                    
                    if i == 0:
                        pred_list = test_logits
                    else:
                        pred_list = np.append(pred_list, test_logits, axis=0)
                
                # 计算指标
                return self.get_sample_scores(epoch, pred_list) 