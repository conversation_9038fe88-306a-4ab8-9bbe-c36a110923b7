import torch
import torch.nn as nn
import torch.nn.functional as F
import math

class ItemRepresentationLearning(nn.Module):
    """
    流行度感知的物品表示学习，结合多视角融合和对比学习
    """
    def __init__(self, args):
        super(ItemRepresentationLearning, self).__init__()
        self.args = args
        self.hidden_size = args.hidden_size
        
        # 基础物品ID嵌入（来自原始模型）
        self.id_embedding = None  # 将在外部设置
        
        # 流行度嵌入层
        self.popularity_embedding = nn.Embedding(args.item_size, args.hidden_size)
        
        # 频域上下文嵌入投影
        self.freq_context_projection = nn.Sequential(
            nn.Linear(args.hidden_size * 3, args.hidden_size),
            nn.ReLU(),
            nn.Linear(args.hidden_size, args.hidden_size)
        )
        
        # 多视角融合层
        self.fusion_layer = nn.Sequential(
            nn.Linear(args.hidden_size * 2, args.hidden_size),
            nn.LayerNorm(args.hidden_size),
            nn.<PERSON>(),
            nn.Linear(args.hidden_size, args.hidden_size)
        )
        
        # 上下文聚合的注意力机制
        self.context_attention = nn.Linear(args.hidden_size, 1)
        
        # 对比学习的温度参数
        self.temperature = nn.Parameter(torch.FloatTensor([0.07]))
        
        # 初始化流行度嵌入
        self._init_popularity_embeddings()
        
    def _init_popularity_embeddings(self):
        """初始化流行度嵌入"""
        self.popularity_embedding.weight.data.normal_(mean=0.0, std=self.args.initializer_range)
        
    def set_id_embedding(self, id_embedding):
        """从主模型设置物品ID嵌入层"""
        self.id_embedding = id_embedding
        
    def extract_freq_context(self, sequences, item_idx):
        """
        为每个物品提取频域上下文
        sequences: [batch, seq_len, hidden] - 序列嵌入
        item_idx: [batch, seq_len] - 序列中的物品索引
        
        返回不同频段的物品上下文嵌入
        """
        batch_size, seq_len, hidden = sequences.shape
        
        # 对每个序列应用FFT
        seq_fft = torch.fft.rfft(sequences, dim=1, norm='ortho')
        seq_magnitude = torch.abs(seq_fft)
        
        # 分为低/中/高频段
        low_idx = seq_len // 6
        high_idx = seq_len // 2
        
        low_freq = torch.mean(seq_magnitude[:, :low_idx, :], dim=1)  # [batch, hidden]
        mid_freq = torch.mean(seq_magnitude[:, low_idx:high_idx, :], dim=1)
        high_freq = torch.mean(seq_magnitude[:, high_idx:, :], dim=1)
        
        # 为每个物品创建上下文字典
        item_low_freq = {}
        item_mid_freq = {}
        item_high_freq = {}
        
        # 收集每个物品的频率表示
        for b in range(batch_size):
            for s in range(seq_len):
                if item_idx[b, s].item() == 0:  # 跳过填充
                    continue
                    
                item_id = item_idx[b, s].item()
                
                if item_id not in item_low_freq:
                    item_low_freq[item_id] = []
                    item_mid_freq[item_id] = []
                    item_high_freq[item_id] = []
                    
                item_low_freq[item_id].append(low_freq[b])
                item_mid_freq[item_id].append(mid_freq[b])
                item_high_freq[item_id].append(high_freq[b])
        
        # 使用注意力机制聚合频率表示
        item_contexts = {}
        for item_id in item_low_freq.keys():
            if len(item_low_freq[item_id]) > 0:
                # 堆叠上下文
                low_contexts = torch.stack(item_low_freq[item_id])  # [n_contexts, hidden]
                mid_contexts = torch.stack(item_mid_freq[item_id])
                high_contexts = torch.stack(item_high_freq[item_id])
                
                # 简单平均进行聚合（可以用注意力机制替代）
                item_low_context = torch.mean(low_contexts, dim=0)
                item_mid_context = torch.mean(mid_contexts, dim=0)
                item_high_context = torch.mean(high_contexts, dim=0)
                
                # 组合不同频率上下文
                combined = torch.cat([item_low_context, item_mid_context, item_high_context], dim=0)
                item_freq_context = self.freq_context_projection(combined)
                
                item_contexts[item_id] = item_freq_context
                
        return item_contexts
    
    def get_popularity_embedding(self, item_ids, item_popularity):
        """
        获取物品的流行度感知嵌入
        item_ids: [batch] - 物品ID
        item_popularity: [item_size] - 每个物品的流行度分数
        """
        # 获取原始流行度嵌入
        pop_emb = self.popularity_embedding(item_ids)
        
        # 根据物品流行度缩放嵌入
        item_pop_values = item_popularity[item_ids].unsqueeze(-1)
        
        # 归一化流行度值
        normalized_pop = item_pop_values / (torch.max(item_popularity) + 1e-8)
        
        # 应用流行度缩放
        scaled_pop_emb = pop_emb * normalized_pop
        
        return scaled_pop_emb
    
    def contrastive_loss(self, item_emb, freq_context_emb, item_ids, item_popularity):
        """
        计算物品嵌入与其频率上下文之间的对比损失
        并使用流行度感知加权
        """
        batch_size = item_emb.size(0)
        
        # 归一化嵌入
        item_emb_norm = F.normalize(item_emb, p=2, dim=1)
        freq_context_emb_norm = F.normalize(freq_context_emb, p=2, dim=1)
        
        # 计算相似度矩阵
        sim_matrix = torch.mm(item_emb_norm, freq_context_emb_norm.transpose(0, 1))
        
        # 按温度缩放
        sim_matrix = sim_matrix / self.temperature
        
        # 获取流行度值进行重新加权
        pop_values = item_popularity[item_ids]
        
        # 计算归一化流行度权重
        pop_weights = 1.0 - (pop_values / (torch.max(pop_values) + 1e-8))
        pop_weights = 0.5 + 0.5 * pop_weights  # 确保权重在0.5到1.0之间
        
        # 创建标签（对角线是正样本对）
        labels = torch.arange(batch_size).to(item_emb.device)
        
        # 使用流行度感知加权计算损失
        loss = F.cross_entropy(sim_matrix, labels, reduction='none')
        weighted_loss = torch.mean(loss * pop_weights)
        
        return weighted_loss
    
    def forward(self, item_ids, item_sequences=None, item_sequence_ids=None, item_popularity=None, freq_contexts=None):
        """
        前向传播获取增强的物品表示
        item_ids: [batch] - 目标物品ID
        item_sequences: [batch, seq_len, hidden] - 序列嵌入（可选，用于上下文提取）
        item_sequence_ids: [batch, seq_len] - 序列中的物品ID（可选）
        item_popularity: [item_size] - 每个物品的流行度分数
        freq_contexts: 预计算频率上下文的字典（可选）
        """
        # 获取ID嵌入
        id_emb = self.id_embedding(item_ids)
        
        # 获取流行度感知嵌入
        pop_emb = self.get_popularity_embedding(item_ids, item_popularity)
        
        # 组合ID和流行度嵌入
        combined_emb = self.fusion_layer(torch.cat([id_emb, pop_emb], dim=1))
        
        # 如果提供了频率上下文或可以提取
        contrastive_loss = None
        if freq_contexts is not None or (item_sequences is not None and item_sequence_ids is not None):
            # 如果未提供则提取上下文
            if freq_contexts is None:
                freq_contexts = self.extract_freq_context(item_sequences, item_sequence_ids)
                
            # 获取当前物品的频率上下文嵌入
            freq_context_embs = []
            for i, item_id in enumerate(item_ids):
                if item_id.item() in freq_contexts:
                    freq_context_embs.append(freq_contexts[item_id.item()])
                else:
                    # 备选方案：如果没有可用的上下文，则使用ID嵌入
                    freq_context_embs.append(id_emb[i].detach())
                    
            freq_context_tensor = torch.stack(freq_context_embs)
            
            # 计算对比损失
            contrastive_loss = self.contrastive_loss(combined_emb, freq_context_tensor, item_ids, item_popularity)
            
        return combined_emb, contrastive_loss 