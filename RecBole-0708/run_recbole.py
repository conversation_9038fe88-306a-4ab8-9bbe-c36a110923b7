# @Time   : 2020/7/20
# <AUTHOR> <PERSON><PERSON><PERSON>
# @Email  : <EMAIL>

# UPDATE
# @Time   : 2022/7/8, 2020/10/3, 2020/10/1
# <AUTHOR> <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>
# @Email  : ch<PERSON><PERSON><PERSON><PERSON><PERSON>@gmail.com, <EMAIL>, <EMAIL>

import argparse

from recbole.quick_start import run

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--model", "-m", type=str, default="BERT4Rec", help="name of models")
    parser.add_argument(
        "--dataset", "-d", type=str, default="Amazon_Office_Products", help="name of datasets"
    )
    parser.add_argument("--config_files", type=str, default=None, help="config files")

    # CFEARec specific arguments
    parser.add_argument("--enable_contrastive", action="store_false",
                       help="enable contrastive learning for CFEARec model")
    parser.add_argument("--contrastive_weight", type=float, default=0.1,
                       help="weight for contrastive learning loss")
    parser.add_argument("--contrastive_temperature", type=float, default=0.09,
                       help="temperature parameter for InfoNCE loss")

    parser.add_argument(
        "--nproc", type=int, default=1, help="the number of process in this group"
    )
    parser.add_argument(
        "--ip", type=str, default="localhost", help="the ip of master node"
    )
    parser.add_argument(
        "--port", type=str, default="5678", help="the port of master node"
    )
    parser.add_argument(
        "--world_size", type=int, default=-1, help="total number of jobs"
    )
    parser.add_argument(
        "--group_offset",
        type=int,
        default=0,
        help="the global rank offset of this group",
    )

    args, _ = parser.parse_known_args()

    config_file_list = (
        args.config_files.strip().split(" ") if args.config_files else None
    )

    # Create config dictionary for CFEARec contrastive learning
    config_dict = {}
    if args.model == "CFEARec":
        # 只有在明确指定了对比学习参数时才覆盖配置文件
        if args.enable_contrastive:
            config_dict.update({
                'use_contrastive_learning': True,
                'contrastive_weight': args.contrastive_weight,
                'contrastive_temperature': args.contrastive_temperature
            })
            print(f"🔥 CFEARec Contrastive Learning ENABLED (via command line)")
            print(f"   - Contrastive Weight: {args.contrastive_weight}")
            print(f"   - Temperature: {args.contrastive_temperature}")
        else:
            # 不覆盖配置文件设置，让配置文件决定是否启用对比学习
            print("📋 Using CFEARec configuration from config file")

    run(
        args.model,
        args.dataset,
        config_dict=config_dict if config_dict else None,
        config_file_list=config_file_list,
        nproc=args.nproc,
        world_size=args.world_size,
        ip=args.ip,
        port=args.port,
        group_offset=args.group_offset,
    )
